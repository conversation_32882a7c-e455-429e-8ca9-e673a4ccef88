# Claude AI Assistant Instructions & Workflow Automation

> **Project:** react-shadcn-platform  
> **Purpose:** Streamlined development workflow with automated context management  
> **Critical:** This file provides persistent context and workflow automation rules  
> **Last Updated:** December 19, 2024

## 🚀 CURRENT PROJECT STATUS

### Active Phase: Phase 1 - Foundation Setup (95% Complete)
**Current Task:** Ready for Task 1.4 - Entity System Integration  
**Branch:** Prototype-2-shadcn  
**Last Completed:** Task 1.3.5 - TanStack Advanced Table UI Fixes

### Next Immediate Tasks:
1. [ ] Task 1.4: Entity System Integration (Port from Prototype 1)
2. [ ] Task 1.5: Basic CRUD Operations
3. [ ] Phase 2: Gita Alumni Mock UI/Wireframes Implementation

## 📋 DEVELOPMENT LIFECYCLE WORKFLOW

### 1. Task Initiation
**AUTOMATIC ACTIONS WHEN STARTING A TASK:**
- Check PROGRESS.md for current task details
- Create task documentation folder if needed
- Run `npm run workflow:check` to verify environment
- Create todo list for task breakdown
- Mark task as "in_progress" in PROGRESS.md

### 2. Implementation Phase
**DURING DEVELOPMENT:**
- Follow existing code patterns and conventions
- Use theme variables (never hardcode colors)
- Maintain component reusability (>85% target)
- Update progress incrementally in todo list
- Run quality checks after each significant change

### 3. Quality Assurance Gates
**BEFORE MARKING TASK COMPLETE:**
```bash
# Mandatory checks - run automatically
npm run lint              # Must pass with 0 errors
npm run type-check        # Must pass with 0 errors
npm run validate:theme    # Must pass theme validation
npm run check:all         # Comprehensive quality check
```

### 4. Manual Testing & Approval
**REQUIRED BEFORE GIT COMMIT:**
- [ ] Await user's manual testing confirmation
- [ ] Get explicit approval: "approved", "sign off", or "commit"
- [ ] Ensure all quality gates have passed
- [ ] Verify no regression in existing functionality

### 5. Git Commit Process
**ONLY AFTER MANUAL APPROVAL:**
```bash
# Use the automated commit workflow
npm run workflow:commit

# Or manual with template:
git add .
git commit -m "Phase X: Task Y.Z - [Description]

- [Change 1]
- [Change 2]
- [Change 3]

Quality Checks: ✅ Lint | ✅ TypeCheck | ✅ Theme | ✅ Manual Testing"
```

## 🎯 WORKFLOW AUTOMATION RULES

### Context Awareness
**I WILL AUTOMATICALLY:**
1. Check current phase and task from PROGRESS.md
2. Reference relevant documentation and guidelines
3. Track progress using TodoWrite tool
4. Run quality checks before suggesting completion
5. Wait for manual testing approval before commits

### Task Progression
**AUTOMATIC TASK FLOW:**
```
Start Task → Update PROGRESS.md → Implement → Quality Checks → 
Manual Testing → User Approval → Git Commit → Update PROGRESS.md → Next Task
```

### Quality Standards
**NON-NEGOTIABLE REQUIREMENTS:**
- ✅ Zero ESLint errors/warnings
- ✅ Zero TypeScript errors
- ✅ Theme validation passes
- ✅ Component reusability >85%
- ✅ Manual testing approved by user
- ✅ No hardcoded colors or styles

## 🔧 PROJECT-SPECIFIC GUIDELINES

## 🚨 CRITICAL THEME RULES

### ❌ **NEVER DO (Zero Tolerance)**
```typescript
// NEVER hardcode colors in style props
backgroundColor: 'hsl(210 25% 11%)'  // ❌ WRONG - breaks theme switching
backgroundColor: '#1f2937'           // ❌ WRONG - hardcoded hex
color: 'rgb(31, 41, 55)'            // ❌ WRONG - hardcoded rgb

// NEVER override theme variables in CSS
:root {
  --muted: 210 40% 96%;      // ❌ WRONG - breaks theme system
  --background: 0 0% 100%;   // ❌ WRONG - prevents dark mode
}
```

### ✅ **ALWAYS DO (Required)**
```typescript
// ✅ USE theme variables in style props
backgroundColor: 'hsl(var(--muted))'      // Muted backgrounds
backgroundColor: 'hsl(var(--background))' // Main backgrounds
color: 'hsl(var(--foreground))'          // Text colors
```

## ✅ **LEGITIMATE EXCEPTIONS (Allowed)**

### 1. **Shadows and Effects**
```typescript
// ✅ ALLOWED - rgba for shadows
boxShadow: '2px 0 4px rgba(0,0,0,0.2)'
boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
```

### 2. **Layout Constants (Non-Color)**
```css
/* ✅ ALLOWED - layout constants in CSS */
:root {
  --radius: 0.5rem;             /* Border radius */
  --table-row-height: 48px;     /* Component sizing */
  --table-selection-width: 48px; /* Component sizing */
}
```

### 3. **Tailwind Classes**
```typescript
// ✅ ALLOWED - Tailwind utility classes
<Badge className="bg-green-500">Success</Badge>
<div className="text-red-500">Error</div>
```

### 4. **Theme Configuration Files**
```typescript
// ✅ ALLOWED - in theme config files only
// src/lib/theme/configs/dark.ts
export const darkTheme = {
  table: {
    container: 'hsl(222.2 84% 4.9%)',  // OK in theme config
    header: 'hsl(217.2 32.6% 17.5%)',  // OK in theme config
  }
}
```

## 🎯 **COMPONENT-SPECIFIC RULES**

### Table Components
```typescript
// ✅ CORRECT for table styling
style={{
  backgroundColor: 'hsl(var(--muted))',     // Headers
  backgroundColor: 'hsl(var(--background))', // Cells
  boxShadow: '2px 0 4px rgba(0,0,0,0.2)',  // Shadows OK
}}
```

### Badge Components
```typescript
// ✅ PREFERRED - use semantic variants
<Badge variant="destructive">Error</Badge>
<Badge variant="secondary">Info</Badge>

// ✅ ALLOWED - Tailwind classes
<Badge className="bg-green-500">Success</Badge>

// ❌ WRONG - hardcoded style props
<Badge style={{ backgroundColor: 'hsl(210 25% 11%)' }}>Wrong</Badge>
```

## 📋 **PRE-EDIT CHECKLIST**

Before making ANY style changes, ask:

1. ✅ Am I using `hsl(var(--variable))` format?
2. ✅ Is this a legitimate exception (shadow, constant, Tailwind)?
3. ✅ Will this work in both light AND dark themes?
4. ✅ Am I following the GUIDELINES_THEME_COMPONENT_ENHANCEMENT.md?

## 🔧 **VALIDATION TOOLS**

Run validation before committing:
```bash
# Check for violations
node validate-theme-usage.js

# Verbose output
node validate-theme-usage.js --verbose

# Auto-fix where possible
node validate-theme-usage.js --fix
```

## 📚 **KEY REFERENCE FILES**

1. **GUIDELINES_THEME_COMPONENT_ENHANCEMENT.md** - Complete rules
2. **TANSTACK_TABLE_ISSUES_ANALYSIS.md** - Known issues and fixes
3. **validate-theme-usage.js** - Automated checking

## 🚨 **COMMON MISTAKES TO AVOID**

1. **Converting colors from screenshots** - Use theme variables instead
2. **"Fixing" styling with hardcoded values** - Find the right theme variable
3. **Conditional hardcoding** - `isPinned ? 'hsl(210...)' : 'hsl(220...)'` is wrong
4. **Overriding in CSS** - Theme variables are managed by theme system

## 💡 **WHY THESE RULES EXIST**

- **Theme switching** must work seamlessly (< 200ms)
- **Dark/light modes** must both work perfectly
- **Maintenance** is easier with centralized theme management
- **Consistency** across all components and themes

## 🎯 **SUCCESS CRITERIA**

- All colors use `var(--variable)` format in components
- No hardcoded HSL/RGB/HEX values in style props
- Theme switching works instantly
- Both light and dark themes look correct

---

**Remember: The user has excellent guidelines. My job is to follow them precisely, understanding both the rules AND the legitimate exceptions.**