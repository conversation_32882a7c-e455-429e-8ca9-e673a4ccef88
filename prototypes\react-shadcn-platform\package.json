{"name": "react-shadcn-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "quality-check": "npm run lint && npm run type-check && npm run build", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format-check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "validate:theme": "node validate-theme-usage.js", "validate:theme:verbose": "node validate-theme-usage.js --verbose", "check:anti-patterns": "bash scripts/check-anti-patterns.sh", "check:large-components": "bash scripts/find-large-components.sh", "check:performance": "bash scripts/find-performance-issues.sh", "check:accessibility": "bash scripts/find-accessibility-issues.sh", "check:all": "npm run lint && npm run type-check && npm run check:anti-patterns", "hook:install": "pip install -r .claude/hooks/requirements.txt", "hook:validate": "python .claude/hooks/validate_all.py", "hook:test": "python -m pytest .claude/hooks/tests/", "hook:safety": "python .claude/hooks/pre_tool_use.py --test", "hook:context": "python .claude/hooks/user_prompt_submit.py --test", "claude:quality": "npm run check:all && npm run validate:theme", "claude:theme": "node validate-theme-usage.js --verbose"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@octokit/rest": "^22.0.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-query": "^5.17.9", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@types/react-router-dom": "^5.3.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "glob": "^11.0.3", "lucide-react": "^0.303.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.62.0", "react-router-dom": "^7.8.1", "tailwind-merge": "^2.2.0", "zod": "^3.25.76", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^24.3.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "vite": "^7.1.3"}}