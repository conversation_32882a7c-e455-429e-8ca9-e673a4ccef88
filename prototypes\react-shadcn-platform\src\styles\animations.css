/* Professional Micro-Animations for Production-Ready Look */

/* Smooth hover effects for all cards */
.card-hover {
  @apply transition-all duration-300 ease-out;
}

.card-hover:hover {
  @apply shadow-xl -translate-y-1;
}

/* Button enhancements */
button {
  @apply transition-all duration-200;
}

button:hover:not(:disabled) {
  @apply scale-105;
}

button:active:not(:disabled) {
  @apply scale-95;
}

/* Pulse animation for badges */
@keyframes pulse-badge {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.badge-pulse {
  animation: pulse-badge 2s ease-in-out infinite;
}

/* Status indicator animations */
@keyframes pulse-dot {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.status-online {
  @apply bg-green-500;
  animation: pulse-dot 2s ease-in-out infinite;
}

.status-away {
  @apply bg-yellow-500;
}

.status-offline {
  @apply bg-gray-400;
}

/* Skeleton loading animation */
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton {
  @apply bg-gradient-to-r from-muted via-muted-foreground/10 to-muted bg-[length:200%_100%];
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

/* Fade in animation for page loads */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

/* Scale in animation for modals/dialogs */
@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

/* Slide animations */
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.4s ease-out;
}

/* Number counter animation */
@keyframes count-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-count {
  animation: count-up 0.6s ease-out;
}

/* Success checkmark animation */
@keyframes check-mark {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.animate-check {
  animation: check-mark 0.4s ease-out;
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text {
  @apply bg-gradient-to-r from-primary via-purple-600 to-primary bg-[size:200%_auto] bg-clip-text text-transparent;
  animation: gradient-shift 3s ease infinite;
}

/* Hover lift effect for interactive elements */
.hover-lift {
  @apply transition-transform duration-200;
}

.hover-lift:hover {
  @apply -translate-y-0.5;
}

/* Focus ring animation */
.focus-ring {
  @apply transition-all duration-200;
}

.focus-ring:focus-visible {
  @apply ring-2 ring-primary ring-offset-2;
}

/* Badge glow effect */
@keyframes badge-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--primary), 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--primary), 0.5);
  }
}

.badge-glow {
  animation: badge-glow 2s ease-in-out infinite;
}

/* Loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced transitions for theme switching */
* {
  @apply transition-colors duration-200;
}

/* Professional input focus states */
input:focus,
textarea:focus,
select:focus {
  @apply outline-none ring-2 ring-primary/20 border-primary;
}

/* Card elevation on hover */
.card-elevated {
  @apply transition-all duration-300;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
}

.card-elevated:hover {
  box-shadow: 
    0 14px 28px rgba(0, 0, 0, 0.25),
    0 10px 10px rgba(0, 0, 0, 0.22);
}

/* Notification bell animation */
@keyframes bell-ring {
  0%, 100% {
    transform: rotate(0);
  }
  10%, 30% {
    transform: rotate(-10deg);
  }
  20%, 40% {
    transform: rotate(10deg);
  }
}

.bell-ring {
  animation: bell-ring 1s ease-in-out;
}

/* Trust metric counter animation */
@keyframes counter-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-counter {
  animation: counter-up 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}